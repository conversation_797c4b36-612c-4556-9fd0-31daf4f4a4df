import createNextIntlPlugin from "next-intl/plugin"
import createMD<PERSON> from '@next/mdx';
import remarkFrontmatter from 'remark-frontmatter';
import remarkGfm from "remark-gfm";
import remarkMdxFrontmatter from 'remark-mdx-frontmatter';

// 添加支持直接使用本地.mdx文件组件
const withMDX = createMDX({
    extension: /\.(md|mdx)$/,
    options: {
        remarkPlugins: [
            remarkGfm,
            remarkFrontmatter,
            remarkMdxFrontmatter
        ],
        rehypePlugins: [],
    },
})
const isProd = process.env.NODE_ENV === 'production';
console.log("###是否编译环境:",isProd)
console.log("###NEXT_PUBLIC_DEV_ORIGIN:",process.env.NEXT_PUBLIC_DEV_ORIGIN)
console.log("###NEXT_PUBLIC_DEFAULT_LOCALE:",process.env.NEXT_PUBLIC_DEFAULT_LOCALE)
// @ts-check
/** @type {import("next").NextConfig} */
const nextConfig = {
	staticPageGenerationTimeout: 5000,
	pageExtensions: ["js", "jsx", "md", "mdx", "ts", "tsx"],
	reactStrictMode: true,
	compress: true,
	// trailingSlash: false,
	...(isProd ? { output: 'export' } : {}),
	transpilePackages: ["next-mdx-remote"],
	trailingSlash: false,
	allowedDevOrigins:['localhost:3000',process.env.NEXT_PUBLIC_DEV_ORIGIN],
	env: {
		NEXT_PUBLIC_WEB_API_URL: process.env.NEXT_PUBLIC_WEB_API_URL,
		NEXT_PUBLIC_DOMAIN: process.env.NEXT_PUBLIC_DOMAIN,
		NEXT_PUBLIC_PROJECT_ID: process.env.NEXT_PUBLIC_PROJECT_ID,
		NEXT_PUBLIC_DEV_ORIGIN:process.env.NEXT_PUBLIC_DEV_ORIGIN
	},
	images: {
		remotePatterns: [
			{
				hostname: 'images.unsplash.com',
			},
			{
				hostname: 'source.unsplash.com',
			},
			{
				hostname: 'public-image.fafafa.ai',
			},
			{
				hostname: 'img.qizhilu.org',
			}
		],
	},
}

const withNextIntl = createNextIntlPlugin("./lib/i18n/request.ts")

export default withMDX(withNextIntl(nextConfig))
