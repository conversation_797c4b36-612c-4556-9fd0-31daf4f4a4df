"use client"

import { useState, useMemo, useEffect } from "react"
import {
	GameCategory,
	ProjectGame,
	GameLocaleContent,
} from "@/lib/types/api-types"
import { getGameLocaleContent } from "@/lib/services/api-client" // Assuming this can be used client-side or we extract its logic
import { Link } from "@/lib/i18n" // Using the project's Link component
import { useTranslations } from "next-intl"

const GAMES_PER_PAGE = 12

interface CategoriesClientViewProps {
	initialCategories: GameCategory[]
	allInitialGames: ProjectGame[]
	locale: string
}

export default function CategoriesClientView({
	initialCategories,
	allInitialGames,
	locale,
}: CategoriesClientViewProps) {
	const tCommon = useTranslations("Common")
	const tGame = useTranslations("Game")

	const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
		null,
	)
	const [searchTerm, setSearchTerm] = useState("")
	const [currentPage, setCurrentPage] = useState(1)

	// Set the first category as selected by default if categories exist
	useEffect(() => {
		if (initialCategories.length > 0 && !selectedCategoryId) {
			setSelectedCategoryId(initialCategories?.[0]?.id ?? null)
		}
	}, [initialCategories, selectedCategoryId])

	const selectedCategory = useMemo(() => {
		if (!selectedCategoryId) return null
		return (
			initialCategories.find((cat) => cat.id === selectedCategoryId) || null
		)
	}, [selectedCategoryId, initialCategories])

	const gamesForSelectedCategoryLocaleFormat = useMemo(() => {
		if (!selectedCategory) return []
		return allInitialGames
			.filter((game) => game.categories?.includes(selectedCategory.id))
			.map((pg) => {
				try {
					// This function might need to be adapted if it has server-side dependencies not available here.
					// For now, assuming it's a pure transformation or can be made so.
					return getGameLocaleContent(locale, pg)
				} catch (e) {
					console.warn(
						`Could not get locale content for game ${pg.id} in locale ${locale}`,
						e,
					)
					return null
				}
			})
			.filter((glc): glc is GameLocaleContent => glc !== null && !!glc.id)
	}, [selectedCategory, allInitialGames, locale])

	const searchedGames = useMemo(() => {
		if (!searchTerm) return gamesForSelectedCategoryLocaleFormat
		return gamesForSelectedCategoryLocaleFormat.filter((game) =>
			game.gameName.toLowerCase().includes(searchTerm.toLowerCase()),
		)
	}, [gamesForSelectedCategoryLocaleFormat, searchTerm])

	const totalPages = useMemo(() => {
		return Math.max(1, Math.ceil(searchedGames.length / GAMES_PER_PAGE))
	}, [searchedGames])

	const paginatedGames = useMemo(() => {
		const startIndex = (currentPage - 1) * GAMES_PER_PAGE
		return searchedGames.slice(startIndex, startIndex + GAMES_PER_PAGE)
	}, [searchedGames, currentPage])

	const handleCategorySelect = (categoryId: string) => {
		setSelectedCategoryId(categoryId)
		setCurrentPage(1)
		setSearchTerm("")
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<div className="mb-8">
				<h2 className="text-2xl font-semibold mb-4 text-center">
					{tCommon("Category")}
				</h2>
				<div className="flex flex-wrap justify-center gap-2">
					{initialCategories.map((cat) => (
						<button
							key={cat.id}
							onClick={() => handleCategorySelect(cat.id)}
							className={`px-4 py-2 rounded-md text-sm font-medium transition-colors
                ${selectedCategoryId === cat.id ? "bg-blue-600 text-white" : "bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"}`}
						>
							{cat.name} ({cat.count ?? 0})
						</button>
					))}
				</div>
			</div>

			{selectedCategory && (
				<div>
					<header className="mb-8 text-center">
						<h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
							{selectedCategory.name}
						</h1>
						{selectedCategory.metadata?.description && (
							<p className="mt-2 max-w-2xl mx-auto text-base text-gray-500 dark:text-gray-400">
								{selectedCategory.metadata.description}
							</p>
						)}
					</header>

					<div className="mb-6">
						<input
							type="text"
							value={searchTerm}
							onChange={(e) => {
								setSearchTerm(e.target.value)
								setCurrentPage(1) // Reset page on new search
							}}
							placeholder={`${tCommon("Search")} ${tGame("Game")}...`}
							className="w-full p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
						/>
					</div>

					{paginatedGames.length > 0 ? (
						<ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
							{paginatedGames.map((game) => (
								<li
									key={game.id}
									className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
								>
									<Link href={`/games/${game.slug}`} className="block">
										{game.gameImages?.[0] ? (
											<img
												src={game.gameImages[0]}
												alt={game.gameName}
												className="w-full h-48 object-cover"
											/>
										) : (
											<div className="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
												<span className="text-gray-500">
													{tGame("noGameVideos")}
												</span>
											</div>
										)}
										<div className="p-4">
											<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 truncate">
												{game.gameName}
											</h3>
											{/* You can add more game details here if needed */}
										</div>
									</Link>
								</li>
							))}
						</ul>
					) : (
						<p className="text-center text-gray-500 dark:text-gray-400 py-10">
							{searchTerm
								? `${tGame("noGameData")} ${tCommon("Search").toLowerCase()} '${searchTerm}'`
								: tGame("noGameData")}
						</p>
					)}

					{totalPages > 1 && (
						<nav
							className="mt-12 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 px-4 sm:px-0"
							aria-label="Pagination"
						>
							<div className="-mt-px w-0 flex-1 flex">
								<button
									onClick={() =>
										setCurrentPage((prev) => Math.max(1, prev - 1))
									}
									disabled={currentPage === 1}
									className="border-t-2 border-transparent pt-4 pr-1 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-gray-500 disabled:opacity-50"
								>
									<svg
										className="mr-3 h-5 w-5 text-gray-400"
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 20 20"
										fill="currentColor"
										aria-hidden="true"
									>
										<path
											fillRule="evenodd"
											d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
											clipRule="evenodd"
										/>
									</svg>
									{tCommon("previous")}
								</button>
							</div>
							<div className="hidden md:-mt-px md:flex">
								{/* Simplified pagination: just page numbers */}
								{[...Array(totalPages).keys()].map((num) => {
									const pageNum = num + 1
									const isCurrent = pageNum === currentPage
									return (
										<button
											key={pageNum}
											onClick={() => setCurrentPage(pageNum)}
											className={`border-t-2 pt-4 px-4 inline-flex items-center text-sm font-medium ${
												isCurrent
													? "border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-300"
													: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-gray-500"
											}`}
											aria-current={isCurrent ? "page" : undefined}
										>
											{pageNum}
										</button>
									)
								})}
							</div>
							<div className="-mt-px w-0 flex-1 flex justify-end">
								<button
									type="button"
									onClick={() =>
										setCurrentPage((prev) => Math.min(totalPages, prev + 1))
									}
									disabled={currentPage === totalPages}
									className="border-t-2 border-transparent pt-4 pl-1 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:border-gray-500 disabled:opacity-50"
								>
									{tCommon("next")}
									<svg
										className="ml-3 h-5 w-5 text-gray-400"
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 20 20"
										fill="currentColor"
										aria-hidden="true"
									>
										<path
											fillRule="evenodd"
											d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
											clipRule="evenodd"
										/>
									</svg>
								</button>
							</div>
						</nav>
					)}
				</div>
			)}
		</div>
	)
}
