
import {
	getLocales,
	getGameCategoriesByLocale,
	getGames,
	getHomePageMetadata,
} from "@/lib/services/api-client"
import {
	GameCategory,
	MetadataInfo,
} from "@/lib/types/api-types"
import { locales as apiLocales } from "@/lib/i18n/locales"
import { getTranslations } from "next-intl/server"
import CategoriesClientView from "./view"
export const dynamic = 'force-static';
// No longer using GAMES_PER_PAGE here as pagination is client-side
// const GAMES_PER_PAGE = 12

export async function generateStaticParams(): Promise<
	{ locale: string }[] // Simplified: only locale is needed as [[...page]] is removed
> {
	return apiLocales.map((locale) => ({ locale }))
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }> // Simplified: no page param needed
}): Promise<MetadataInfo> {
	const { locale } = await params
	const t = await getTranslations({ locale, namespace: "Common" }) // Specify namespace if needed
	const tBlogPage = await getTranslations({ locale, namespace: "BlogPage" })

	// Metadata for the main "All Categories" page
	const homeMetadata = await getHomePageMetadata(locale).catch(() => null)
	return {
		title: t("Category") || "Game Categories",
		description:
			tBlogPage("description") || "Explore all game categories.", // Using BlogPage.description for a general category page description
		ogTitle: t("Category") || "Game Categories",
		ogDescription:
			tBlogPage("description") || "Explore all game categories.",
		...(homeMetadata ? { ogImage: homeMetadata.ogImage } : {}),
	}
}

export default async function CategoriesOverviewPage({
	params,
}: {
	params: Promise<{ locale: string }> // Simplified: no page param needed
}) {
	const { locale } = await params

	// Fetch all categories for the locale
	const categories = await getGameCategoriesByLocale(locale)

	// Fetch all games for the locale
	// This might be a large dataset. Consider if this is feasible or if games should be fetched on demand client-side after category selection.
	// For now, following the request to fetch all server-side.
	const allGames = await getGames().catch((err) => {
		console.error(`Error fetching all games for locale ${locale}:`, err)
		return [] // Return empty array, client component will handle empty game states
	})

	if (categories.length === 0 && allGames.length === 0) {
		// A more specific check if both fail, or if categories are essential
		// If no categories, the client component will show an empty state for categories.
		// If categories exist but games fail, it will show categories but no games for them.
	}

	return (
		<CategoriesClientView
			initialCategories={categories}
			allInitialGames={allGames}
			locale={locale}
		/>
	)
} 