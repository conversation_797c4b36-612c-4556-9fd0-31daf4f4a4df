"use client";
import React from 'react';
import { GameFilterGrid } from '@/lib/components/ui/view/GameFilterGrid';
import { GameTag,GameLocaleContent } from '@/lib/types';

// 定义组件 Props
interface TagViewProps {
  tag: GameTag;
  games: GameLocaleContent[];
}

export const TagView: React.FC<TagViewProps> = ({
  tag,
  games
}) => {
  return (
    <div>
      {/* Tag header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{tag.name}</h1>
        {tag.description && (
          <p className="text-gray-600">{tag.description}</p>
        )}
      </div>

      {/* Game filter grid component */}
      <GameFilterGrid
        games={games}
        title="All Games"
      />
    </div>
  );
};
