import React from "react"
import { <PERSON>ada<PERSON> } from "next"
import { Breadcrumb } from "@/lib/components/ui/view/Breadcrumb"
import {
	getGameTagList,
	getGameTagsByLocale,
	getGameTagDetailBySlug,
	getGames,
	getHomePageMetadata,
	getDefaultLocaleByAPI,
} from "@/lib/services/api-client"
import { alternatesLanguage, locales as apiLocales, defaultLocale } from "@/lib/i18n/locales"
import { setRequestLocale, getTranslations } from "next-intl/server"
import { TagView } from "./view"
import {
	GameTag,
	BreadcrumbItem,
	GameLocaleContent,
} from "@/lib/types"
export const dynamic = 'force-static';
type Props = {
	params: Promise<{ locale: string; slug: string }>
}

export async function generateStaticParams() {
	try {
		// Get all available tags from API (all languages)
		const tagList = await getGameTagList()
		const params = []
		if (!tagList || tagList.length === 0) {
			console.warn("generateStaticParams: No tags found, returning empty params.");
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})	
			}
			return params
		}
		// 判断默认语言的标签是否为空，如果为空，则返回空
		const defaultLocaleTag = tagList.find((item) => item.locale === defaultLocale)
		if (!defaultLocaleTag || defaultLocaleTag.tags.length === 0) {
			console.warn("generateStaticParams: default locale No tags found, returning empty params.");
			for (const locale of apiLocales) {
				params.push({
					locale,
					slug: "index"
				})	
			}
			return params
		}
		// Generate all possible combinations of locale and tag slug
	
		for (const locale of apiLocales) {
			// 查找当前语言的标签
			const localeData = tagList.find((item) => item.locale === locale)
			if (localeData?.tags) {
				for (const tag of localeData.tags) {
					params.push({
						locale,
						slug: tag.slug,
					})
				}
			}
		}

		return params
	} catch (error) {
		console.error("Failed to fetch params for generateStaticParams:", error)
		return []
	}
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale, slug } = await params
	if (!locale) {
		return { title: "Locale Not Found" }
	}

	setRequestLocale(locale)

	// Get metadata from API with tag slug
	const tag: GameTag = await getGameTagDetailBySlug(locale, slug)
	const languages = await alternatesLanguage(tag.slug)
	return {
		title: tag.metaTitle || `${slug} Games`,
		description: tag.metaDescription || `Browse all games tagged with ${slug}`,
		openGraph: {
			title: tag.metaTitle || `${slug} Games`,
			description:
				tag.metaDescription || `Browse all games tagged with ${slug}`,
			images: tag.imageUrl ? [{ url: tag.imageUrl }] : undefined,
			url: tag.slug,
		},
		twitter: {
			card: "summary" as
				| "summary"
				| "summary_large_image"
				| "app"
				| "player",
			title: tag.metaTitle || `${slug} Games`,
			description:
				tag.metaDescription || `Browse all games tagged with ${slug}`,
			images: tag.imageUrl ? [tag.imageUrl] : undefined,
		},
		alternates: {
			languages,
		},
	}
}

export default async function TagDetailPage({ params }: Props) {
	const { locale, slug } = await params
	const tag: GameTag = await getGameTagDetailBySlug(locale, slug)
	const t = await getTranslations()
	// 获取面包屑数据
	const breadcrumbItems: BreadcrumbItem[] = [
		{ label: t("Common.Home"), href: "/" },
		{ label: tag.name, href: `${tag.slug}` },
	]
	// 获取当前语言的所有标签
	const tags = await getGameTagsByLocale(locale)

	// 获取所有游戏
	const allGames = await getGames()

	// 筛选出包含当前标签的游戏
	const tagGames = allGames
		.filter((game) => game?.tags?.includes(tag.id))
		.map((game) => {
			// 获取当前语言的游戏内容
			const gameLocale = game.gameLocales?.find((gl) => gl.locale === locale)
			return gameLocale?.content
		})
		.filter(Boolean) as GameLocaleContent[]

	return (
		<>
			<Breadcrumb items={breadcrumbItems} />
			<main className="container mx-auto px-4 py-6">
				<TagView tag={tag} games={tagGames} />
			</main>
		</>
	)
}
