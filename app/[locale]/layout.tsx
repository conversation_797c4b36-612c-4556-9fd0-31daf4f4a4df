import { cn } from "@/lib/utils/react"
import { NextIntlClientProvider } from "next-intl"
import { getMessages, setRequestLocale } from "next-intl/server"
import { alternatesLanguage } from "@lib/i18n"
import { Metadata } from "next"
import type { PropsWithChildren } from "react"
import { Providers } from "./providers"
import { DynamicHeader } from "@/lib/components/ui/view/DynamicHeader"
import { DynamicFooter } from "@/lib/components/ui/view/DynamicFooter"
import FontConfigGenerator from "@/lib/components/FontConfigGenerator"
import ThemeConfigGenerator from "@/lib/components/ThemeConfigGenerator"
import {
	getSiteSettings,
	getNavItems,
	getHomePageMetadata,
} from "@/lib/services/api-client"
import { GoogleAnalytics, MicrosoftClarity } from "@/lib/components/analytics"
import Script from "next/script"
import { checkProperty } from "@/lib/utils/react"
// 动态导入当前主题
import "./current-theme.css"
import "./font-theme.css"

type Props = PropsWithChildren<{ params: Promise<{ locale: string }> }>

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const siteSettings = await getSiteSettings()
	const siteMetadata = await getHomePageMetadata(locale)
	checkProperty(siteSettings, "siteName", "请在网站设置中添加网站名称")
	checkProperty(siteMetadata, "title", "请在网站设置中添加网站标题")
	checkProperty(siteMetadata, "description", "请在网站设置中添加网站描述")

	return {
		title: siteMetadata?.title ?? siteSettings.siteName,
		description: siteMetadata?.description ?? siteSettings.siteName,
		alternates: {
			languages: alternatesLanguage(""),
		},
		icons: {
			icon: siteSettings.icons?.favicon,
			apple: siteSettings.icons?.appleTouchIcon,
			shortcut: siteSettings.icons?.androidIcon,
		},
		robots: {
			index: false,
			follow: true,
		},
	}
}

export default async function LocaleLayout({ children, params }: Props) {
	const messages = await getMessages()
	const { locale } = await params

	// 直接从第三方API获取Header、Footer和网站配置数据
	const siteSettings = await getSiteSettings()
	const siteMetadata = await getHomePageMetadata(locale)

	// 判断是否为开发环境
	const isDev = process.env.NODE_ENV === "development"

	// 在构建时生成字体配置文件
	await FontConfigGenerator({ siteSettings })

	// 在构建时生成主题配置文件
	await ThemeConfigGenerator({ siteSettings })
	const navItems = await getNavItems(locale)
	return (
		<html suppressHydrationWarning lang={locale}>
			<head />
			<body
				className={cn(
					"min-h-screen font-sans antialiased",
					"bg-background dark:bg-tech-dark dark:text-white",
				)}
			>
				<NextIntlClientProvider locale={locale} messages={messages}>
					<Providers locale={locale}>
						<DynamicHeader navItem={navItems} siteSettings={siteSettings} />
						{children}
						<DynamicFooter
							siteSettings={siteSettings}
							siteMetadata={siteMetadata}
						/>

						{/* 统计代码，仅在非开发环境下加载 */}
						{!isDev && (
							<>
								{siteSettings.analytics?.gaId && (
									<GoogleAnalytics gaId={siteSettings.analytics.gaId} />
								)}
								{siteSettings.analytics?.plausible && (
									<Script
										defer
										data-domain={siteSettings.domain}
										src={siteSettings.analytics.plausible}
									/>
								)}
								{siteSettings.analytics?.clarityId && (
									<MicrosoftClarity
										clarityId={siteSettings.analytics.clarityId}
									/>
								)}
								{siteSettings.analytics?.adsenseClientId && (
									<Script
										async
										src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${siteSettings.analytics.adsenseClientId}`}
										crossOrigin="anonymous"
									/>
								)}
							</>
						)}
					</Providers>
				</NextIntlClientProvider>
			</body>
		</html>
	)
}
