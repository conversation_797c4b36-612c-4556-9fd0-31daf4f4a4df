import React from 'react';
import { Metada<PERSON> } from 'next';
import { alternatesCanonical, locales as apiLocales, defaultLocale, generateHreflangData } from "@/lib/i18n/locales";
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Breadcrumb } from '@/lib/components/ui/view/Breadcrumb';
import { Search, Calendar, User, Clock, Tag, ChevronRight } from 'lucide-react';
import {Link} from '@lib/i18n';
import Image from 'next/image';
import { getArticleByLocale } from '@/lib/services/api-client';
import { BlogPost } from '@/lib/types';

export const dynamic = 'force-static';

type Props = {
  params: Promise<{ locale: string; page?: string[] }>
}

export async function generateStaticParams() {
  try {
    // Get all blog posts to determine total pages
    const allPosts: BlogPost[] = await getArticleByLocale(defaultLocale);

    // Calculate total pages (assuming 5 posts per page)
    const postsPerPage = 10;
    const totalPages = Math.ceil(allPosts.length / postsPerPage);

    // Generate params for each locale and page
    const params = [];

    // First add the main blog page (no page number)
    for (const locale of apiLocales) {
      params.push({
        locale,
        page: []
      });
    }

    // Then add each page number
    for (const locale of apiLocales) {
      for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
        params.push({
          locale,
          page: [pageNum.toString()]
        });
      }
    }

    return params;
  } catch (error) {
    console.error("Failed to fetch data for generateStaticParams:", error);
    return [];
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale, page } = await params;
  setRequestLocale(locale);

  // Determine page number
  const pageNumber = page && page.length > 0 ? parseInt(page[0] || "1", 10) : 1;

  // Build URL path for alternates - Simplified as full alternate logic might need more context
  let path = '/blogs';
  if (pageNumber > 1) {
    path += `/${pageNumber}`;
  }
  // const languages = await alternatesLanguage(path); // Removed, as getArticleDetail was removed
  const t = await getTranslations("BlogPage");
  // Set title based on page number
  const baseTitle = t('blog');
  const title = pageNumber > 1
    ? `${baseTitle} - Page ${pageNumber}`
    : `${baseTitle} - Latest News and Insights`;

  const description = t('description');
  // 获取 hreflang 数据
  const hreflangData =  generateHreflangData(path)
  // 获取 canonical 数据
  const canonical =  alternatesCanonical(locale, path)

  return {
    title,
    description,
    alternates: {
      languages: hreflangData.languages,
      canonical
    },
  };
}

export default async function BlogPage({ params }: Props) {
  const { locale, page } = await params;
  setRequestLocale(locale);
  const t = await getTranslations();

  // Determine page number
  const pageNumber = page && page.length > 0 ? parseInt(page[0] || "1", 10) : 1;
  const postsPerPage = 10;

  // Fetch blog posts from API
  const allPosts: BlogPost[] = await getArticleByLocale(locale);

  // Get featured posts (always the first 3)
  const featuredPosts: BlogPost[] = allPosts.slice(0, 3);

  // Calculate pagination
  const startIndex = (pageNumber - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const paginatedPosts: BlogPost[] = allPosts.slice(startIndex, endIndex);
  const totalPages = Math.ceil(allPosts.length / postsPerPage);

  // Fetch blog categories - Derived from allPosts
  const categoryCounts: { [key: string]: number } = {};
  allPosts.forEach(post => {
    if (post.category) {
      categoryCounts[post.category.slug] = (categoryCounts[post.category.slug] || 0) + 1;
    }
  });

  const categories = Object.entries(categoryCounts).map(([name, count]) => ({
    name,
    slug: name,
    count,
  }));

  // Popular tags (could be fetched from API in the future or derived from posts)
  const popularTags = [
    'Gaming', 'Strategy', 'RPG', 'Esports', 'Game Design',
    'Reviews', 'Tutorials', 'Industry News', 'Mobile Gaming', 'PC Gaming'
  ];

  // Breadcrumb items
  const breadcrumbItems = pageNumber > 1
    ? [
      { label: t('Common.Home'), href: '/' },
      { label: t('Common.Blog'), href: '/blogs' },
      { label: t('Common.Page') + ` ${pageNumber}`, isActive: true, href: `/blogs/${pageNumber}` },
    ]
    : [
      { label: t('Common.Home'), href: '/' },
      { label: t('Common.Blog'), isActive: true, href: '/blogs' },
    ];

  // Function to get pagination URL
  const getPaginationUrl = (page: number) => {
    return page === 1 ? '/blog' : `/blog/${page}`;
  };

  return (
    <>
      <Breadcrumb items={breadcrumbItems} />

      <main className="container mx-auto px-4 py-8">
        {/* Page title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('BlogPage.blog')}</h1>
          <p className="text-gray-600">
            {t('BlogPage.description')}
          </p>
        </div>

        {/* Search */}
        <div className="relative max-w-xl mx-auto mb-12">
          <input
            type="text"
            placeholder="Search articles..."
            className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm"
          />
          <Search className="absolute left-4 top-3.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Featured posts - only show on first page */}
        {pageNumber === 1 && (
          <section className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">{t('BlogPage.featuredArticles')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <Link
                  key={post.id}
                  href={`${post.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow">
                    <div className="relative h-48 md:h-56">
                      <Image
                        src={post.titleImageUrl || '/placeholder-image.jpg'}
                        alt={post.title}
                        fill
                        className="object-cover transition-transform group-hover:scale-105 duration-300"
                      />
                      <div className="absolute top-0 left-0 bg-indigo-600 text-white text-xs font-bold px-3 py-1 m-3 rounded">
                        {post.category?.name}
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="font-bold text-xl text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {post.metadata.description}
                      </p>
                      <div className="flex items-center text-xs text-gray-500">
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          <span>{post.author}</span>
                        </div>
                        <span className="mx-2">•</span>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          <span>{post.updateTime}</span>
                        </div>
                        <span className="mx-2">•</span>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )}

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main content */}
          <div className="lg:w-2/3">
            {/* Latest articles */}
            <section className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {pageNumber > 1 ? `Articles - Page ${pageNumber}` : 'Latest Articles'}
              </h2>

              <div className="space-y-8">
                {paginatedPosts.map((post: BlogPost) => (
                  <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="md:flex">
                      <div className="md:w-1/3 relative h-48 md:h-auto">
                        <Image
                          src={post.titleImageUrl || '/placeholder-image.jpg'}
                          alt={post.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="p-6 md:w-2/3">
                        <div className="flex items-center text-xs text-gray-500 mb-3">
                          <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">
                            {post.category?.name || 'General'}
                          </span>
                          <span className="mx-2">•</span>
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>{post.updateTime}</span>
                          </div>
                        </div>

                        <Link href={`/blog/${post.slug}`}>
                          <h3 className="font-bold text-xl text-gray-900 mb-2 hover:text-indigo-600 transition-colors">
                            {post.title}
                          </h3>
                        </Link>

                        <p className="text-gray-600 text-sm mb-4">
                          {post.metadata.description}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-xs text-gray-500">
                            <div className="flex items-center">
                              <User className="h-3 w-3 mr-1" />
                              <span>{post.author || 'Admin'}</span>
                            </div>
                            <span className="mx-2">•</span>
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              <span>{post.readTime || 'N/A'}</span>
                            </div>
                          </div>

                          <Link
                            href={`${post.slug}`}
                            className="text-indigo-600 text-sm font-medium flex items-center hover:text-indigo-800 transition-colors"
                          >
                            {t("BlogPage.readMore")}
                            <ChevronRight className="h-4 w-4 ml-1" />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </div>

              {/* Pagination */}
              <div className="flex justify-center mt-12">
                <nav className="flex items-center">
                  {pageNumber > 1 && (
                    <Link
                      href={getPaginationUrl(pageNumber - 1)}
                      className="px-4 py-2 border border-gray-300 rounded-l-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      {t('Common.previous')}
                    </Link>
                  )}

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pageNumber <= 3) {
                      pageNum = i + 1;
                    } else if (pageNumber >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = pageNumber - 2 + i;
                    }

                    return (
                      <Link
                        key={pageNum}
                        href={getPaginationUrl(pageNum)}
                        className={`px-4 py-2 border-t border-b border-r ${pageNum === pageNumber
                            ? 'border-indigo-500 bg-indigo-50 text-indigo-600'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                          } text-sm font-medium`}
                      >
                        {pageNum}
                      </Link>
                    );
                  })}

                  {pageNumber < totalPages && (
                    <Link
                      href={getPaginationUrl(pageNumber + 1)}
                      className="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      {t('Common.next')}
                    </Link>
                  )}
                </nav>
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3">
            {/* Categories */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">{t("BlogPage.categories")}</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <Link
                    key={category.slug}
                    href={`${category.slug}`}
                    className="flex items-center justify-between py-2 hover:text-indigo-600 transition-colors"
                  >
                    <span className="text-gray-700">{category.name}</span>
                    <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Popular tags */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Popular Tags</h3>
              <div className="flex flex-wrap gap-2">
                {popularTags.map((tag, index) => (
                  <Link
                    key={index}
                    href={`/blog/tag/${tag.toLowerCase().replace(/\s+/g, '-')}`}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-indigo-100 hover:text-indigo-700 transition-colors"
                  >
                    <div className="flex items-center">
                      <Tag className="h-3 w-3 mr-1" />
                      <span>{tag}</span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Newsletter */}
            <div className="bg-indigo-600 rounded-lg shadow-md p-6 text-white">
              <h3 className="text-lg font-bold mb-2">Subscribe to Our Newsletter</h3>
              <p className="text-indigo-100 text-sm mb-4">
                Get the latest gaming news, tips, and exclusive content delivered to your inbox.
              </p>
              <form>
                <div className="mb-3">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-indigo-300"
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-white text-indigo-600 font-medium px-4 py-2 rounded-lg hover:bg-indigo-50 transition-colors"
                >
                  Subscribe
                </button>
              </form>
            </div>

            {/* Featured article */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden mt-6">
              <div className="relative h-48">
                <Image
                  src="https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                  alt="Editor's Pick"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6">
                  <span className="text-white text-xs font-bold mb-2">EDITOR'S PICK</span>
                  <h3 className="text-white font-bold text-lg">The Future of Cloud Gaming: What to Expect in 2024</h3>
                  <Link
                    href="/blog/future-cloud-gaming"
                    className="text-indigo-300 text-sm mt-2 hover:text-white transition-colors inline-flex items-center"
                  >
                    Read Article
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
