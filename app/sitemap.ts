export const dynamic = "force-static";
import { MetadataRoute } from 'next';
import { locales, defaultLocale } from '@/lib/i18n/locales';
import {
  getGames,
  getGameCategoriesByLocale,
  getArticleByLocale,
} from '@/lib/services/api-client';

// 定义页面信息类型
type PageInfo = {
  path: string;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
};

/**
 * 生成动态的 sitemap.xml
 * 包含所有页面的 URL，包括不同语言版本
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || '';

  // 获取所有游戏、分类和博客文章
  const [games, categories, blogPosts] = await Promise.all([
    getGames(),
    getGameCategoriesByLocale(defaultLocale),
    getArticleByLocale(defaultLocale)
  ]);

  // 计算博客分页
  const postsPerPage = 5;
  const totalBlogPages = Math.ceil(blogPosts.length / postsPerPage);

  // 定义页面信息
  const pageInfos: PageInfo[] = [
    // 首页
    { path: '', changeFrequency: 'daily', priority: 1.0 },

    // 博客主页
    { path: '/blogs', changeFrequency: 'daily', priority: 0.9 },
  ];

  // 添加博客分页
  for (let page = 2; page <= totalBlogPages; page++) {
    pageInfos.push({
      path: `/blog/${page}`,
      changeFrequency: 'daily',
      priority: 0.7,
    });
  }

  // 添加游戏页面
  for (const game of games) {
    pageInfos.push({
      path: `${game.slug}`,
      changeFrequency: 'weekly',
      priority: 0.8,
    });
  }

  // 添加分类页面
  for (const category of categories) {
    // 主分类页面
    pageInfos.push({
      path: `${category.slug}`,
      changeFrequency: 'weekly',
      priority: 0.7,
    });

  }

  // 添加博客文章页面
  for (const post of blogPosts) {
    pageInfos.push({
      path: `${post.slug}`,
      changeFrequency: 'monthly',
      priority: 0.6,
    });
  }

  // 创建 sitemap 条目数组
  const entries: MetadataRoute.Sitemap = [];

  // 为每个页面和每种语言创建条目
  for (const pageInfo of pageInfos) {
    for (const locale of locales) {
      const localizedPath = locale === defaultLocale
        ? pageInfo.path
        : `/${locale}${pageInfo.path}`;

      entries.push({
        url: `${baseUrl}${localizedPath}`,
        lastModified: new Date(),
        changeFrequency: pageInfo.changeFrequency,
        priority: pageInfo.priority,
      });
    }
  }

  return entries;
}
