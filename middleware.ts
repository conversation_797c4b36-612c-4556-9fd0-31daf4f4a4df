import { routing } from "@/lib/i18n/routing"
import createMiddleware from "next-intl/middleware"
import type { NextRequest } from "next/server"

export const config = {
	// Match only internationalized pathnames
	matcher: ["/((?!api|public|_next|auth|.*\\..*).*)"],
}

const handleRouting = createMiddleware(routing)
export default function middleware(request: NextRequest) {
	return handleRouting(request)
}
