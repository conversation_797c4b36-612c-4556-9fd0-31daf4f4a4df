"use client"
import React, { useEffect } from "react"
import { Icon } from "@/lib/components/common/Icon"
import { GameDetailContent, GameDetailContentType } from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface ContentNavigationTabsProps {
	tabs: GameDetailContent[]
	activeTabId?: string // Optional: Control active tab from parent
	onTabClick?: (tabId: string) => void // Optional: Handle tab clicks in parent
}

export const ContentNavigationTabs: React.FC<ContentNavigationTabsProps> = ({
	tabs = [],
	activeTabId, // Use this if provided
	onTabClick,
}) => {
	console.log("tabs", tabs)

	// 如果没有提供 activeTabId，则使用第一个标签页的 id
	const currentActiveTabId = activeTabId ?? tabs[0]?.tabId
	
	// 处理平滑滚动到对应内容区域
	const scrollToContent = (tabId: string) => {
		const element = document.getElementById(tabId)
		if (element) {
			// 使用平滑滚动效果
			element.scrollIntoView({
				behavior: "smooth",
				block: "start"
			})
		}
	}
	
	// 如果URL中包含hash，初始化时滚动到对应位置
	useEffect(() => {
		if (window.location.hash) {
			const hash = window.location.hash.substring(1) // 去掉#号
			const element = document.getElementById(hash)
			if (element) {
				// 添加一点延迟，确保页面完全加载
				setTimeout(() => {
					element.scrollIntoView({
						behavior: "smooth",
						block: "start"
					})
				}, 300)
			}
		}
	}, [])

	return (
		<div className="mb-8">
			<div className="border-b border-gray-200 dark:border-gray-700">
				{/* Use overflow-x-auto for smaller screens */}
				<ul
					className="flex flex-nowrap -mb-px text-sm font-medium text-center overflow-x-auto"
					id="content-tabs"
				>
					{tabs && (tabs.map((tab) => {
						const isActive = tab.tabId === currentActiveTabId
						return (
							<li className="mr-2 flex-shrink-0" key={tab.tabId}>
								{" "}
								{/* Added flex-shrink-0 */}
								<a
									href={`#${tab.tabId}`} // Keep href for accessibility and non-JS fallback
									onClick={(e) => {
								// 总是阻止默认行为，使用自定义滚动
								e.preventDefault()
								
								// 如果提供了外部处理函数，则调用
								if (onTabClick) {
									onTabClick(tab.tabId)
								}
								
								// 平滑滚动到目标位置
								scrollToContent(tab.tabId)
							}}
									className={`inline-flex items-center justify-center p-4 border-b-2 rounded-t-lg group whitespace-nowrap ${
										isActive
											? "border-primary text-primary dark:border-primary dark:text-primary active"
											: "border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 dark:hover:border-gray-600"
									}`}
									aria-current={isActive ? "page" : undefined}
								>
									{tab.icon && (
										<Icon
											name={tab.icon}
											className={`w-4 h-4 mr-2 ${isActive ? "text-primary dark:text-primary" : "text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300"}`}
											size={16}
										/>
									)}
									{tab.title}
								</a>
							</li>
						)
					}))}
				</ul>
			</div>
		</div>
	)
}

export default ContentNavigationTabs
