// lib/components/view/HomePageContent/components/GamePlayerInfo.tsx
"use client"
import React from "react"
import { Maximize, Share2, Heart, MessageSquare } from "lucide-react"
import { useTranslations } from "next-intl"
import { GameTag } from "@/lib/types/api-types"
import { Link } from "@/lib/i18n"
import { cn } from "@/lib/utils/react"

interface GamePlayerInfoProps {
	gameTitle: string
	ratingElement: React.ReactNode // Pass the rendered stars
	tags: GameTag[] // Add tags prop
	gameSettings: Record<string, string>
	onFullscreen?: () => void // 添加全屏回调函数
	onShare?: () => void // 添加分享回调函数
	onFavorite?: () => void // 添加收藏回调函数
	onComment?: () => void // 添加评论回调函数
	isFavorited?: boolean // 是否已收藏
}
export const GamePlayerInfo: React.FC<GamePlayerInfoProps> = ({
	gameTitle,
	ratingElement,
	tags,
	gameSettings,
	onFullscreen,
	onShare,
	onFavorite,
	onComment,
	isFavorited = false,
}) => {
	const t = useTranslations()

	return (
		<div className="mb-6 mt-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
			<div className="flex-grow w-full space-y-2">
				<div className="flex flex-wrap items-center justify-between">
					<div className="flex items-center gap-x-4">
						<h1 className="text-2xl font-bold text-foreground mr-4">
							{gameTitle}
						</h1>
						{ratingElement}
					</div>
					<div className="flex space-x-2">
						<button
							className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
							aria-label={t("Common.fullscreen")}
							type="button"
							onClick={onFullscreen}
						>
							<Maximize className="h-5 w-5" />
						</button>
						<button
							className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
							aria-label={t("Common.share")}
							type="button"
							onClick={onShare}
						>
							<Share2 className="h-5 w-5" />
						</button>
						<button
							className={cn(
								"p-2 rounded-full flex items-center justify-center hover:bg-muted transition-colors disabled:opacity-70 disabled:cursor-not-allowed",
								isFavorited ? "text-red-500" : "text-primary",
							)}
							aria-label={t("Common.favorite")}
							type="button"
							onClick={onFavorite}
						>
							<Heart
								className={cn(
									"h-5 w-5",
									isFavorited ? "fill-red-500" : "",
								)}
							/>
						</button>
						<button
							className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
							aria-label={t("Common.comments")}
							type="button"
							onClick={onComment}
						>
							<MessageSquare className="h-5 w-5" />
						</button>
					</div>
				</div>
				{gameSettings && (
					<div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-muted-foreground">
						{Object.entries(gameSettings).map(([key, value]) => (
							<div key={key}>
								<span>
									{t(`Game.${key}`)}: {value}
								</span>
							</div>
						))}
					</div>
				)}
				{tags && tags.length > 0 && (
					<div className="flex flex-wrap gap-1.5">
						{tags.map((tag) => (
							<Link
								key={tag.id}
								href={tag.slug}
								className="bg-muted text-muted-foreground px-2 py-0.5 rounded text-xs hover:bg-muted/80 transition-colors"
							>
								{tag.name}
							</Link>
						))}
					</div>
				)}
			</div>
		</div>
	)
}

export default GamePlayerInfo
