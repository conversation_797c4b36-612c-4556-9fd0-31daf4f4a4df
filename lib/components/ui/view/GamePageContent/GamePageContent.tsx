"use client"
import React, { useMemo, useRef, useState, useEffect } from "react" // Import useMemo for stable random index
import { Icon } from "@/lib/components/common"
import GameIframe, { GameIframeRef } from "../GameIframe"
import GameComments from "../GameComments" // Verify path
import GamePlayerInfo from "./components/GamePlayerInfo"
import ContentNavigationTabs from "./components/ContentNavigationTabs"
import RightSidebar from "../RightSidebar"
import { Link } from "@i18n/navigation"
import GameCard from "../GameCard" // Assuming GameCard exists and is imported
import FloatingAction from "./components/FloatingAction"
import MarkdownRenderer from "@/lib/components/common/MarkdownRenderer"
import { getGameLocaleContent } from "@/lib/services/api-client"
import ReactPlayer from "react-player"
import { siteSettings } from "@/lib/config/siteSettings"
// 导入API类型定义
import {
	ProjectGame,
	GameLocaleContent,
	TabContent,
	GameVideo,
	GameInfoData,
	GameDetailContent,
	GameTag,
	GameDetailContentType,
} from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface AdCode {
	id: string
	position: string
	code: string
}

// Star组件
const Star = ({ className }: { className?: string }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		fill="none"
		stroke="currentColor"
		strokeWidth="2"
		strokeLinecap="round"
		strokeLinejoin="round"
		className={className}
	>
		<title>Star</title>
		<polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
		<defs>
			<linearGradient id="half-star" x1="0%" y1="0%" x2="100%" y2="0%">
				<stop offset="50%" stopColor="currentColor" stopOpacity="1" />
				<stop offset="50%" stopColor="currentColor" stopOpacity="0" />
			</linearGradient>
		</defs>
	</svg>
)

// Props Interface
interface GamePageContentProps {
	locale: string
	projectGame: ProjectGame // 直接从服务端传入游戏信息
	gameLocaleContent: GameLocaleContent // 直接从服务端传入本地化内容
	adCodes?: AdCode[] // 广告代码，可选
	allGames?: ProjectGame[] // 所有游戏信息，用于获取关联游戏的详细信息
}

// 注意：图标映射已移至ContentNavigationTabs组件内部

export const GamePageContent: React.FC<GamePageContentProps> = ({
	locale,
	projectGame,
	gameLocaleContent,
	adCodes = [],
	allGames = [],
}) => {
	const t = useTranslations("Game")
	// 从传入的数据中提取所需信息
	const gameName = gameLocaleContent.gameName
	// 推荐游戏卡片数据 - 从allGames中获取关联游戏的详细信息
	const gameCards: GameLocaleContent[] = useMemo(() => {
		if (!projectGame.relatedGames || projectGame.relatedGames.length === 0) {
			return []
		}
		return projectGame.relatedGames
			.map((gameId) => {
				// 在allGames中查找对应的游戏
				const relatedGame = allGames.find((game) => game.id === gameId)
				// 如果找到了游戏，使用其详细信息
				if (relatedGame) {
					// 查找对应语言的本地化内容
					const localeContent: GameLocaleContent = getGameLocaleContent(
						locale,
						relatedGame,
					)

					return localeContent
				}
				console.log(`获取推荐游戏列表时，没有找到游戏:${gameId} 对应的游戏详情`)
				return {} as GameLocaleContent
			})
			.filter((game) => game.id !== null)
	}, [projectGame.relatedGames, allGames, gameLocaleContent.id])

	// 相似游戏数据 - 使用相同的游戏卡片数据，但确保类型兼容RightSidebar组件
	const similarGames = gameCards

	// 游戏iframe数据
	const gameIframeData: GameInfoData = projectGame.gameInfo

	// 游戏标签
	const tags: GameTag[] = gameLocaleContent.gameTags || []

	// 游戏tabs内容（包含推荐游戏、是否显示评论、是否显示相关视频）
	// 数据在api服务端已经拼接好了
	const tabContents: GameDetailContent[] = gameLocaleContent.contents
	// 针对推荐游戏、相关视频、评论，这三个标签页的icon和title需要补充国际化
	tabContents.map((tab) => {
		if (tab.type === GameDetailContentType.RelatedGames) {
			tab.icon = "star"
			tab.title = t("relatedGames")
		}
		if (tab.type === GameDetailContentType.RelatedVideos) {
			tab.icon = "video"
			tab.title = t("relatedVideos")
		}
		if (tab.type === GameDetailContentType.Comments) {
			tab.icon = "comment"
			tab.title = t("comments")
		}
		return tab
	})
	// 游戏视频
	const gameVideos = projectGame.relatedVideos || []

	// 渲染星级评分
	const renderStars = () => {
		// 使用游戏信息中的评分
		const rating = projectGame.gameInfo?.settings?.rating || 4.5
		const fullStars = Math.floor(rating)
		const hasHalfStar = rating % 1 >= 0.5
		const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)

		return (
			<div className="flex items-center text-primary mr-4">
				{[...Array(fullStars)].map((_, i) => (
					<Star key={`f-${i}`} className="h-5 w-5 fill-current" />
				))}
				{hasHalfStar && (
					<Star key="half" className="h-5 w-5 fill-[url('#half-star')]" />
				)}
				{[...Array(emptyStars)].map((_, i) => (
					<Star key={`e-${i}`} className="h-5 w-5" />
				))}
				<span className="ml-2 text-muted-foreground">{rating.toFixed(1)}</span>
			</div>
		)
	}

	// 获取指定位置的广告代码
	const getAdCode = (position: string) => {
		const ad = adCodes.find((ad) => ad.position === position)
		return ad ? ad.code : null
	}

	// 根据ID获取Tab内容 - 用于动态渲染时查找特定标签
	// 注意：现在我们使用map直接渲染所有标签，不再需要这个函数
	// 但保留它以便将来可能需要单独获取某个标签内容

	// --- Prepare items for the 8-slot recommendation grid ---
	// --- Prepare items for the 8-slot recommendation grid ---
	const gridItems = useMemo(() => {
		const numTotalSlots = 8
		const numGamesToConsider = 6
		const gamesToDisplay = gameCards.slice(0, numGamesToConsider)
		const items = Array(numTotalSlots).fill(null)

		// 只有当游戏总数大于等于7个时才显示"查看更多"选项
		const shouldShowViewMore = gameCards.length >= 7
		const viewMoreSlotIndex = numTotalSlots - 1 // 最后一个位置用于"查看更多"

		// 如果游戏数量超过考虑数量，添加广告
		if (gamesToDisplay.length > numGamesToConsider) {
			const adSlotIndex = 6 // 广告固定在倒数第二个位置
			items[adSlotIndex] = { type: "ad", id: "ad-slot" }
		}

		// 只有当游戏总数大于等于7个时才添加"查看更多"选项
		if (shouldShowViewMore) {
			items[viewMoreSlotIndex] = { type: "viewMore", id: "view-more-slot" }
		}

		// Fill remaining slots with games or empty placeholders
		let gameDataIndex = 0
		for (let i = 0; i < numTotalSlots; i++) {
			if (items[i] === null) {
				// If the slot is not yet filled
				if (gameDataIndex < gamesToDisplay.length) {
					const game = gamesToDisplay[gameDataIndex]
					if (game) {
						items[i] = { type: "game", ...game, id: game.id }
						gameDataIndex++
					}
				} else {
					items[i] = { type: "empty", id: `empty-${i}` }
				}
			}
		}
		return items
	}, [gameCards]) // Recalculate if gameCards changes

	// 创建一个ref用于控制iframe全屏
	const gameIframeRef = useRef<GameIframeRef>(null)

	// 处理全屏显示的回调函数
	const handleFullscreen = () => {
		if (gameIframeRef.current) {
			gameIframeRef.current.toggleFullscreen()
		} else {
			console.log("全屏切换失败：iframe引用不可用")
		}
	}

	const handleShare = () => {
		// 获取当前游戏信息
		const gameTitle = gameLocaleContent.gameName
		const gameDescription = gameLocaleContent.gameDescription || ""
		const currentUrl = typeof window !== "undefined" ? window.location.href : ""

		// 使用站点的Twitter配置
		const twitterHandle = siteSettings?.socialLinks?.twitter
			? new URL(siteSettings.socialLinks.twitter).pathname.replace(/\//g, "")
			: ""

		// 构建Twitter分享URL
		const twitterShareUrl = new URL("https://twitter.com/intent/tweet")
		const params = new URLSearchParams()

		// 添加分享文本
		params.append("text", `${gameTitle} - ${gameDescription.substring(0, 100)}${gameDescription.length > 100 ? "..." : ""}`)

		// 添加分享链接
		params.append("url", currentUrl)

		// 如果有Twitter账号，添加via参数
		if (twitterHandle) {
			params.append("via", twitterHandle)
		}

		// 添加标签
		if (gameLocaleContent.gameTags && gameLocaleContent.gameTags.length > 0) {
			const hashtags = gameLocaleContent.gameTags
				.slice(0, 3) // 最多取3个标签
				.map(tag => tag.name.replace(/\s+/g, ""))
				.join(",")
			if (hashtags) {
				params.append("hashtags", hashtags)
			}
		}

		twitterShareUrl.search = params.toString()

		// 打开分享窗口
		window.open(
			twitterShareUrl.toString(),
			"_blank",
			"width=550,height=420,resizable=yes,scrollbars=yes"
		)
	}

	// 使用cookies保存收藏游戏状态
	const [isFavorited, setIsFavorited] = useState(false)
	const FAVORITE_COOKIE_NAME = "favorite_games"
	const COOKIE_EXPIRY_DAYS = 30

	// 初始化时从cookies中读取收藏状态
	useEffect(() => {
		if (typeof window === "undefined") return

		// 从cookies中读取收藏的游戏列表
		const getCookieValue = (name: string) => {
			const value = `; ${document.cookie}`
			const parts = value.split(`; ${name}=`)
			if (parts.length === 2) return parts.pop()?.split(";").shift()
			return ""
		}

		const favoriteGames = getCookieValue(FAVORITE_COOKIE_NAME)
		if (favoriteGames) {
			try {
				const gamesArray = JSON.parse(favoriteGames)
				// 检查当前游戏是否在收藏列表中
				setIsFavorited(gamesArray.includes(projectGame.id))
			} catch (e) {
				console.error("解析收藏游戏cookie失败", e)
			}
		}
	}, [projectGame.id])

	const handleFavorite = () => {
		if (typeof window === "undefined") return

		// 从cookies中读取收藏的游戏列表
		const getCookieValue = (name: string) => {
			const value = `; ${document.cookie}`
			const parts = value.split(`; ${name}=`)
			if (parts.length === 2) return parts.pop()?.split(";").shift()
			return ""
		}

		// 设置cookie
		const setCookie = (name: string, value: string, days: number) => {
			const date = new Date()
			date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
			const expires = `expires=${date.toUTCString()}`
			document.cookie = `${name}=${value}; ${expires}; path=/`
		}

		// 获取当前收藏的游戏列表
		let favoriteGames: string[] = []
		const favoriteGamesStr = getCookieValue(FAVORITE_COOKIE_NAME)
		if (favoriteGamesStr) {
			try {
				favoriteGames = JSON.parse(favoriteGamesStr)
			} catch (e) {
				console.error("解析收藏游戏cookie失败", e)
			}
		}

		// 切换收藏状态
		if (isFavorited) {
			// 如果已收藏，则移除
			favoriteGames = favoriteGames.filter(id => id !== projectGame.id)
		} else {
			// 如果未收藏，则添加
			favoriteGames.push(projectGame.id)
		}

		// 更新cookie
		setCookie(FAVORITE_COOKIE_NAME, JSON.stringify(favoriteGames), COOKIE_EXPIRY_DAYS)
		
		// 更新状态
		setIsFavorited(!isFavorited)
	}

	const handleComment = () => {
		console.log("暂不实现")
	}

	// 数据检查 - 由于数据是从服务端传入，不需要加载状态和错误处理
	// 但仍然检查数据是否存在
	if (!gameIframeData) {
		return (
			<div className="flex justify-center items-center min-h-[400px] text-muted-foreground">
				{t("noGameData")}
			</div>
		)
	}

	return (
		<div className="mb-10">
			{/* ... (Top content: flex wrapper, left side with Iframe, PlayerInfo, Tabs) */}
			<div className="flex gap-6">
				<div className="w-full md:w-3/4 flex flex-col">
					{/* Game Iframe */}
					<GameIframe
						ref={gameIframeRef}
						gameUrl={gameIframeData.gameUrl}
						gameTitle={gameName}
						gameImage={gameLocaleContent.gameImages?.[0] || ""}
						description={gameLocaleContent.gameDescription}
					/>

					{/* Game Player Info */}
					<GamePlayerInfo
						gameTitle={gameName}
						gameSettings={projectGame.gameInfo.settings}
						ratingElement={renderStars()}
						tags={tags}
						onFullscreen={handleFullscreen}
						onShare={handleShare}
						onFavorite={handleFavorite}
						onComment={handleComment}
						isFavorited={isFavorited}
					/>

					{/* Content Navigation Tabs */}
					<ContentNavigationTabs tabs={tabContents} />

					{/* Tab 内容区域 */}
					<div>
						{/* 所有内容都通过tab.type渲染 */}

						{/* 动态渲染Tab内容 - 根据tab.type类型渲染不同内容 */}
						{tabContents.map((tab, index) => {
							// 跳过没有内容的标签
							if (!tab.text && !tab.jsonContent) return null

							// 获取当前tab对应的tabItem，以获取type属性
							const tabType = tab.type

							return (
								<div
									key={tab.tabId}
									id={tab.tabId}
									className="mb-10 scroll-mt-24"
								>
									<div className="bg-card rounded-xl shadow-md overflow-hidden p-6">
										<h2 className="text-2xl font-bold mb-4 text-foreground">
											{tab.title || tab.tabId}
										</h2>

										{/* 根据tab.type类型渲染不同内容 */}
										{tabType === GameDetailContentType.RelatedGames ? (
											// 渲染推荐游戏内容 - 使用原来的gridItems模式
											<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
												{gridItems.map((item) => {
													if (!item) return null

													switch (item.type) {
														case "game":
															const { type, ...gameProps } = item
															return <GameCard key={item.id} {...gameProps} />
														case "ad":
															return (
																<div
																	key={item.id}
																	className="bg-muted rounded-lg shadow-md flex items-center justify-center p-4 h-full"
																>
																	<span className="text-muted-foreground text-center text-sm">
																		{t("ad")}
																	</span>
																</div>
															)
														case "viewMore":
															return (
																<Link
																	key={item.id}
																	href="/categories"
																	className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow flex flex-col items-center justify-center p-4 text-primary hover:bg-muted group h-full"
																>
																	<div className="flex flex-col items-center justify-center h-full">
																		<div className="rounded-full bg-primary/10 p-4 mb-2">
																			<Icon
																				name="ArrowRight"
																				className="h-6 w-6 text-primary group-hover:translate-x-1 transition-transform"
																				size={24}
																			/>
																		</div>
																		<span className="font-medium text-center text-sm">
																			{t("viewMore")}{" "}
																		</span>
																	</div>
																</Link>
															)
														case "empty":
															return null
														default:
															return null
													}
												})}
											</div>
										) : tabType === GameDetailContentType.RelatedVideos ? (
											// 渲染游戏视频内容
											<div>
												{gameVideos.length > 0 ? (
													<div className="grid grid-cols-1 gap-4 mt-6 md:grid-cols-3 md:pb-2">
														{gameVideos.map((video, index) => (
															<div
																key={`video-${index + 1}`}
																className="flex-1"
															>
																<div className="aspect-w-16 aspect-h-9 mb-2">
																	<ReactPlayer
																		url={video.url}
																		controls
																		width="100%"
																		height="auto"
																		className="rounded-lg"
																	></ReactPlayer>
																</div>
															</div>
														))}
													</div>
												) : (
													gameVideos.length === 0 && (
														<div className="text-center text-muted-foreground py-4">
															{t("noGameVideos")}
														</div>
													)
												)}
											</div>
										) : tabType === GameDetailContentType.Comments ? (
											// 渲染玩家评论区块
											<div className="w-full rounded-lg">
												<GameComments />
											</div>
										) : (
											// 默认渲染文章内容（MDX格式）
											<MarkdownRenderer
												content={tab.text || ""}
												className="prose dark:prose-invert prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-ul:text-foreground prose-li:text-foreground prose-a:text-primary prose-img:my-4 max-w-none"
												variant={tab.tabId === "faq" ? "faq" : "default"}
											/>
										)}
									</div>
									{/* 在每2块内容之后插入广告 */}
									{index % 2 === 0 && (
										<div
											className="mt-6 mb-10 bg-accent/20 rounded-lg p-6 text-center border-2 border-accent/30"
											dangerouslySetInnerHTML={{
												__html:
													getAdCode("content-middle") ||
													'<div class="text-accent-foreground font-bold text-xl">广告位 - 内容嵌入广告</div>',
											}}
										></div>
									)}
								</div>
							)
						})}
					</div>
				</div>{" "}
				{/* End lg:w-3/4 */}
				{/* Right Sidebar */}
				<RightSidebar allGames={allGames} locale={locale} />
			</div>{" "}
			{/* End flex gap-6 */}
			{/* Floating Action Buttons */}
			<FloatingAction />
		</div> // End main wrapper mb-10
	)
}
