// lib/components/view/GameComments.tsx
'use client'; // Assuming client-side interaction for comments

import React, { useState } from 'react';
import Image from 'next/image';
import { Icon } from '@/lib/components/common';
import { useTranslations } from 'next-intl';
interface Comment {
  id: string;
  author: string;
  avatar: string;
  timestamp: string;
  text: string;
  likes: number;
  dislikes: number;
  replies?: Comment[];
}



// Dummy data for comments
const initialComments: Comment[] = [
  {
    id: 'c1',
    author: '游戏玩家Alice',
    avatar: 'https://source.unsplash.com/random/40x40?avatar&sig=1',
    timestamp: '2小时前',
    text: '这款游戏太棒了！画面精美，玩法有趣，强烈推荐！希望开发者能加入更多关卡。',
    likes: 15,
    dislikes: 1,
    replies: [
      {
        id: 'c1r1',
        author: '开发者Bob',
        avatar: 'https://source.unsplash.com/random/40x40?avatar&sig=dev',
        timestamp: '1小时前',
        text: '感谢您的反馈！我们正在努力开发新内容，敬请期待！',
        likes: 5,
        dislikes: 0,
      },
    ],
  },
  {
    id: 'c2',
    author: '评论员Charlie',
    avatar: 'https://source.unsplash.com/random/40x40?avatar&sig=2',
    timestamp: '5小时前',
    text: '游戏整体不错，但是感觉难度曲线有点陡峭，新手可能会觉得困难。希望能调整一下平衡性。',
    likes: 8,
    dislikes: 3,
  },
];

export const GameComments: React.FC = () => {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null); // ID of comment being replied to
  const t = useTranslations();
  const handlePostComment = () => {
    if (!newComment.trim()) return;
    const commentToAdd: Comment = {
      id: `c${Date.now()}`,
      author: '当前用户', // Replace with actual user later
      avatar: 'https://source.unsplash.com/random/40x40?avatar&sig=user',
      timestamp: '刚刚',
      text: newComment,
      likes: 0,
      dislikes: 0,
    };
    // Add reply logic later if replyingTo is set
    setComments([commentToAdd, ...comments]);
    setNewComment('');
    setReplyingTo(null);
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <article key={comment.id} className={`p-4 text-base bg-card rounded-lg ${isReply ? 'ml-6 lg:ml-12 mt-4' : 'mb-6'}`}>
      <footer className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <p className="inline-flex items-center mr-3 text-sm text-foreground font-semibold">
            <img className="mr-2 w-6 h-6 rounded-full" src={comment.avatar} alt={comment.author} />
            {comment.author}
          </p>
          <p className="text-sm text-muted-foreground"><time dateTime={comment.timestamp} title={comment.timestamp}>{comment.timestamp}</time></p>
        </div>
        {/* Add dropdown for report/edit later */}
      </footer>
      <p className="text-foreground">{comment.text}</p>
      <div className="flex items-center mt-4 space-x-4">
        <button type="button" className="flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline">
          <Icon name="ThumbsUp" className="mr-1.5 w-3.5 h-3.5" size={14} /> {comment.likes} {t('Common.like') || '赞'}
        </button>
        <button type="button" className="flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline">
          <Icon name="ThumbsDown" className="mr-1.5 w-3.5 h-3.5" size={14} /> {comment.dislikes} {t('Common.dislike') || '踩'}
        </button>
        <button
          type="button"
          className="flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline"
          onClick={() => setReplyingTo(comment.id)} // Simple reply trigger
        >
          <Icon name="CornerDownRight" className="mr-1.5 w-3.5 h-3.5" size={14} />
          {t('Common.reply') || '回复'}
        </button>
      </div>
      {comment.replies && comment.replies.map(reply => renderComment(reply, true))}
      {replyingTo === comment.id && (
        <div className="mt-4 ml-6 lg:ml-12"> {/* Basic reply form placeholder */}
          <textarea
            rows={2}
            className="w-full px-3 py-2 text-sm text-foreground bg-muted rounded-lg border border-border focus:ring-primary focus:border-primary"
            placeholder={`${t('Common.replyingTo') || '回复'} ${comment.author}...`}
          />
          <button type="button" className="mt-2 text-xs bg-primary text-primary-foreground py-1 px-3 rounded hover:bg-primary/90">{t('Common.postReply') || '发布回复'}</button>
           <button type="button" onClick={() => setReplyingTo(null)} className="ml-2 text-xs text-muted-foreground hover:text-foreground hover:underline">{t('Common.cancel') || '取消'}</button>
        </div>
      )}
    </article>
  );

  return (
    <section  className="bg-muted rounded-2xl shadow-inner p-4">
      <div className="mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg lg:text-2xl font-bold text-foreground">{t('Common.commentsTitle') || '评论'} ({comments.length})</h2>
        </div>
        <form className="mb-6">
          <div className="py-2 px-4 mb-4 bg-card rounded-lg rounded-t-lg border border-border">
            <label htmlFor="comment" className="sr-only">{t('Common.yourComment') || '你的评论'}</label>
            <textarea
              id="comment"
              rows={4}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="px-0 w-full text-sm text-foreground bg-transparent border-0 focus:ring-0 focus:outline-none"
              placeholder={t('Common.writeCommentPlaceholder') || '写下你的评论...'}
              required
            ></textarea>
          </div>
          <button
            type="button" // Change to type="submit" when backend is ready
            onClick={handlePostComment}
            className="inline-flex items-center py-2.5 px-4 text-xs font-medium text-center text-primary-foreground bg-primary rounded-lg focus:ring-4 focus:ring-primary/20 hover:bg-primary/90"
          >
             <Icon name="Send" className="h-4 w-4 mr-1" size={16} /> {t('Common.postCommentButton') || '发布评论'}
          </button>
        </form>

        {comments.map(comment => renderComment(comment))}

      </div>
    </section>
  );
};

export default GameComments;
