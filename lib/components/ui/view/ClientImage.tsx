'use client';

import React from 'react';

interface ClientImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
}

/**
 * 客户端图片组件，处理图片加载错误
 */
export const ClientImage: React.FC<ClientImageProps> = ({ 
  src, 
  alt, 
  className, 
  fallbackSrc = '/placeholder-image.png' 
}) => {
  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = e.target as HTMLImageElement;
    target.onerror = null;
    if (fallbackSrc) {
      target.src = fallbackSrc;
    }
  };

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      onError={handleError}
    />
  );
};

export default ClientImage;
