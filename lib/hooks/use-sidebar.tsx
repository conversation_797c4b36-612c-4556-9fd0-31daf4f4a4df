"use client"

import * as React from "react"
import { useSidebar as useOriginalSidebar } from "@/lib/components/ui/sidebar"

// 扩展原始 sidebar 钩子的类型
interface ExtendedSidebarHook {
  // 原始 sidebar 钩子的所有属性
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
  
  // 扩展的属性
  isExpanded: boolean
  setIsExpanded: (expanded: boolean) => void
  isGameBox: boolean
  setIsGameBox: (isGameBox: boolean) => void
}

/**
 * 扩展的 sidebar 钩子
 * 添加了游戏盒子相关的状态和方法
 */
export function useSidebar(): ExtendedSidebarHook {
  // 使用原始的 sidebar 钩子
  const originalSidebar = useOriginalSidebar()
  
  // 添加游戏盒子状态
  const [isGameBox, setIsGameBox] = React.useState(false)
  
  // 将原始的 open 状态映射为 isExpanded
  const isExpanded = originalSidebar.open
  const setIsExpanded = originalSidebar.setOpen
  
  // 返回扩展的钩子
  return {
    ...originalSidebar,
    isExpanded,
    setIsExpanded,
    isGameBox,
    setIsGameBox,
  }
}
