import { defaultLocale, locales } from "@/lib/i18n/locales"
import { getRequestConfig } from "next-intl/server"

// 导入本地国际化文件
import zhMessages from "@/messages/zh-CN.json"
import enMessages from "@/messages/en.json"

// 本地国际化文件映射(必须包含工具当中定义的所有语言，这些属于模板当中直接使用到的国际化内容，不需要通过api获取)
const localMessages: Record<string, any> = {
	'zh-CN': zhMessages,
	'en': enMessages,
	//TODO:添加其他语言
}


export default getRequestConfig(async ({ requestLocale }) => {
	let locale = await requestLocale
	// 确保使用有效的语言
	const defaultLocales = locales

	if (!locale || !defaultLocales.includes(locale as any)) {
		locale = defaultLocale
	}

	try {
		// 加载请求的语言消息
		// 获取本地消息
		const messages = localMessages[locale] || {};
		return { locale, messages }
	} catch (error) {
		console.error(
			`无法加载 ${locale} 的翻译文件，将使用默认语言 ${defaultLocale}`,
		)

		// 加载默认语言消息作为备份
		const fallbackMessages = localMessages[defaultLocale] || {}

		return { locale: defaultLocale, messages: fallbackMessages }
	}
})
